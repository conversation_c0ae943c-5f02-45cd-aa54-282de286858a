Stack trace:
Frame         Function      Args
0007FFFF9EE0  00021006116E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8DE0) msys-2.0.dll+0x2116E
0007FFFF9EE0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFF9EE0  0002100469F2 (00021028DF99, 0007FFFF9D98, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9EE0  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFF9EE0  00021006A525 (0007FFFF9EF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFF9EF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF950A10000 ntdll.dll
7FF94F990000 KERNEL32.DLL
7FF94DE00000 KERNELBASE.dll
7FF94FE10000 USER32.dll
7FF94E2A0000 win32u.dll
7FF94F5A0000 GDI32.dll
7FF94DCE0000 gdi32full.dll
7FF94DB20000 msvcp_win.dll
7FF94DBC0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF94E940000 advapi32.dll
7FF94FAF0000 msvcrt.dll
7FF9501C0000 sechost.dll
7FF94E410000 bcrypt.dll
7FF9508B0000 RPCRT4.dll
7FF94D330000 CRYPTBASE.DLL
7FF94E4C0000 bcryptPrimitives.dll
7FF94EA00000 IMM32.DLL
